@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-gray-100 font-sans antialiased;
  }
}

@layer components {
  .neumorphic-card {
    @apply bg-gray-100 rounded-neumorphic shadow-neumorphic;
  }

  .neumorphic-button {
    @apply bg-gray-100 rounded-xl shadow-neumorphic hover:shadow-neumorphic-sm active:shadow-neumorphic-inset transition-all duration-200;
  }

  .neumorphic-input {
    @apply bg-gray-100 rounded-xl shadow-neumorphic-inset border-none focus:outline-none focus:ring-2 focus:ring-primary-500;
  }

  .glass-effect {
    @apply bg-white/20 backdrop-blur-sm border border-white/30;
  }
}
