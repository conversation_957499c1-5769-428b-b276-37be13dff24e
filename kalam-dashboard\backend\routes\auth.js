const express = require('express');
const { login, getMe, register } = require('../controllers/authController');
const auth = require('../middleware/auth');
const { isAdmin } = require('../middleware/role');

const router = express.Router();

// @route   POST /api/auth/login
router.post('/login', login);

// @route   GET /api/auth/me
router.get('/me', auth, getMe);

// @route   POST /api/auth/register (Admin only)
router.post('/register', auth, isAdmin, register);

module.exports = router;
