import React, { useState, useEffect } from 'react';
import { Plus, Filter, Users, Phone, MapPin, Calendar, AlertCircle, CheckCircle, Loader } from 'lucide-react';
import { Button } from '../components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';
import { Select } from '../components/ui/Select';
import { Modal } from '../components/ui/Modal';
import { Input } from '../components/ui/Input';
import { familiesAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const Families = () => {
  const [families, setFamilies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [selectedCenter, setSelectedCenter] = useState('All Centers');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [newFamily, setNewFamily] = useState({
    name: '',
    contact: '',
    center: '',
    address: ''
  });
  const { user } = useAuth();

  // Available centers based on user role
  const availableCenters = user?.role === 'admin'
    ? ['Delhi Center', 'Mumbai Center', 'Bangalore Center']
    : [user?.center].filter(Boolean);

  // Fetch families on component mount
  useEffect(() => {
    const fetchFamilies = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('🔄 Fetching families...');
        const response = await familiesAPI.getFamilies();
        console.log('📋 Families response:', response);

        if (response.success) {
          setFamilies(response.data.families || []);
          console.log('✅ Families loaded successfully:', response.data.families?.length || 0);
        } else {
          throw new Error(response.message || 'Failed to fetch families');
        }
      } catch (error) {
        console.error('❌ Error fetching families:', error);
        setError(error.message);
        setFamilies([]);
      } finally {
        setLoading(false);
      }
    };

    fetchFamilies();
  }, []);

  const filteredFamilies = selectedCenter === 'All Centers'
    ? families
    : families.filter(family => family.center === selectedCenter);

  const handleAddFamily = async (e) => {
    e.preventDefault();

    try {
      setSubmitting(true);
      setError(null);
      setSuccess(null);

      // Validate required fields
      if (!newFamily.name || !newFamily.contact || !newFamily.center || !newFamily.address) {
        throw new Error('All fields are required');
      }

      // For tutors, ensure they're creating family for their center
      if (user.role === 'tutor' && newFamily.center !== user.center) {
        throw new Error('You can only create families for your assigned center');
      }

      console.log('➕ Creating family:', newFamily);
      const response = await familiesAPI.createFamily(newFamily);
      console.log('✅ Family created:', response);

      if (response.success) {
        // Add the new family to the current list
        setFamilies(prevFamilies => [response.data.family, ...prevFamilies]);

        // Reset form and close modal
        setNewFamily({ name: '', contact: '', center: '', address: '' });
        setIsAddModalOpen(false);
        setSuccess('Family created successfully!');

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error(response.message || 'Failed to create family');
      }
    } catch (error) {
      console.error('❌ Error creating family:', error);
      setError(error.message);
    } finally {
      setSubmitting(false);
    }
  };

  const getProgressBadge = (member) => {
    if (member.role === 'student') {
      if (member.attendance >= 90) return { variant: 'success', text: 'Excellent' };
      if (member.attendance >= 75) return { variant: 'primary', text: 'Good' };
      return { variant: 'warning', text: 'Needs Attention' };
    } else {
      if (member.trainingStatus === 'completed') return { variant: 'success', text: 'Completed' };
      if (member.trainingStatus === 'in-progress') return { variant: 'primary', text: 'In Progress' };
      return { variant: 'warning', text: 'Started' };
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Family Management</h1>
          <p className="text-gray-600">Manage and track Kalam families and their members</p>
        </div>
        <Button onClick={() => setIsAddModalOpen(true)} variant="kalam">
          <Plus className="h-4 w-4 mr-2" />
          Add Family
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
          <div>
            <h3 className="text-red-800 font-medium">Error</h3>
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
          <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
          <div>
            <h3 className="text-green-800 font-medium">Success</h3>
            <p className="text-green-700 text-sm">{success}</p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <Loader className="h-8 w-8 animate-spin text-indigo-600" />
          <span className="ml-2 text-gray-600">Loading families...</span>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-center">
            <Filter className="h-5 w-5 text-gray-500" />
            <Select
              value={selectedCenter}
              onChange={(e) => setSelectedCenter(e.target.value)}
              className="w-full sm:w-auto"
            >
              {centers.map(center => (
                <option key={center} value={center}>{center}</option>
              ))}
            </Select>
            <div className="text-sm text-gray-600">
              Showing {filteredFamilies.length} families
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Families Grid */}
      {!loading && (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredFamilies.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No families found</h3>
              <p className="text-gray-600 mb-4">
                {selectedCenter === 'All Centers'
                  ? 'Get started by adding your first family.'
                  : `No families found in ${selectedCenter}.`
                }
              </p>
              <Button onClick={() => setIsAddModalOpen(true)} variant="kalam">
                <Plus className="h-4 w-4 mr-2" />
                Add First Family
              </Button>
            </div>
          ) : (
            filteredFamilies.map((family) => (
              <Card key={family._id} className="hover:shadow-neumorphic-lg transition-all duration-200">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="text-lg">{family.name}</span>
                    <Badge variant="primary">{family.totalMembers || 0} members</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Family Info */}
                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-gray-600">
                        <Phone className="h-4 w-4 mr-2" />
                        {family.contact}
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin className="h-4 w-4 mr-2" />
                        {family.center}
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Calendar className="h-4 w-4 mr-2" />
                        Registered: {new Date(family.createdAt).toLocaleDateString()}
                      </div>
                      {family.address && (
                        <div className="text-sm text-gray-600">
                          <strong>Address:</strong> {family.address}
                        </div>
                      )}
                    </div>

                {/* Members */}
                <div className="pt-3 border-t border-gray-200">
                  <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    Family Members
                  </h4>
                  <div className="space-y-2">
                    {family.members.map((member) => {
                      const badge = getProgressBadge(member);
                      return (
                        <div key={member.id} className="neumorphic-card p-3 rounded-lg">
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="font-medium text-gray-900">{member.name}</p>
                              <p className="text-sm text-gray-600">
                                {member.role === 'student' ? member.education : member.skill}
                              </p>
                              {member.role === 'student' && (
                                <p className="text-xs text-gray-500">
                                  Attendance: {member.attendance}% | Last Score: {member.lastTestScore}
                                </p>
                              )}
                            </div>
                            <Badge variant={badge.variant} className="text-xs">
                              {badge.text}
                            </Badge>
                          </div>
                        </div>
                      );
                    })}
                    {family.members.length === 0 && (
                      <p className="text-sm text-gray-500 italic">No members added yet</p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add Family Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add New Family"
        className="max-w-lg"
      >
        <form onSubmit={handleAddFamily} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Family Name
            </label>
            <Input
              value={newFamily.name}
              onChange={(e) => setNewFamily({...newFamily, name: e.target.value})}
              placeholder="Enter family name"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Contact Number
            </label>
            <Input
              type="tel"
              value={newFamily.contact}
              onChange={(e) => setNewFamily({...newFamily, contact: e.target.value})}
              placeholder="10-digit contact number"
              pattern="[0-9]{10}"
              maxLength="10"
              required
            />
            <p className="text-xs text-gray-500 mt-1">Enter a 10-digit mobile number</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Center
            </label>
            <Select
              value={newFamily.center}
              onChange={(e) => setNewFamily({...newFamily, center: e.target.value})}
              required
            >
              <option value="">Select a center</option>
              {availableCenters.map(center => (
                <option key={center} value={center}>{center}</option>
              ))}
            </Select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Address
            </label>
            <textarea
              value={newFamily.address}
              onChange={(e) => setNewFamily({...newFamily, address: e.target.value})}
              placeholder="Enter complete address"
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              rows="3"
              required
            />
          </div>

          {/* Error display in modal */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              variant="kalam"
              className="flex-1"
              disabled={submitting}
            >
              {submitting ? (
                <>
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Family
                </>
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsAddModalOpen(false);
                setNewFamily({ name: '', contact: '', center: '', address: '' });
                setError(null);
              }}
              className="flex-1"
              disabled={submitting}
            >
              Cancel
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default Families;
