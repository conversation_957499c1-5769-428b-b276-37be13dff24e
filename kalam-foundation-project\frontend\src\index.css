@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-100 font-sans antialiased;
  }
}

@layer components {
  .neumorphic-card {
    @apply bg-gray-100 rounded-neumorphic shadow-neumorphic;
  }

  .neumorphic-button {
    @apply bg-gray-100 rounded-xl shadow-neumorphic hover:shadow-neumorphic-sm active:shadow-neumorphic-inset transition-all duration-200;
  }

  .neumorphic-input {
    @apply bg-gray-100 rounded-xl shadow-neumorphic-inset border-none focus:outline-none focus:ring-2 focus:ring-primary-500;
  }

  .glass-effect {
    @apply bg-white/20 backdrop-blur-sm border border-white/30;
  }

  /* Chatbot Animations */
  .animate-slideUp {
    animation: slideUp 0.3s ease-out;
  }

  .animate-bounce {
    animation: bounce 1.4s infinite;
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
